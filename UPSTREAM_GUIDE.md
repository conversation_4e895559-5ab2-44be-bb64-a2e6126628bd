# 如何从上游（Upstream）仓库同步更新

本文档旨在解释如何将原始模板仓库（我们称之为 `upstream`）的更新同步到您自己的项目仓库（我们称之为 `origin`）。

## 核心概念

*   **`origin`**: 这是您自己项目在 GitHub 上的远程仓库。您拥有该仓库的读写权限，您所有的开发工作和提交都将推送到这里。
*   **`upstream`**: 这是您项目最初来源的模板仓库。通常您对它只有只读权限。我们将其添加为一个远程仓库，目的是为了能够从中拉取最新的更新。

这种设置可以让您的项目保持独立，同时又能方便地获取原始模板的更新。

## 操作步骤

### 1. 添加上游仓库（只需操作一次）

如果您还没有添加过 `upstream` 远程仓库，您需要先执行以下命令：

```bash
# 将 "https://github.com/lifefloating/nestjs-project-template/" 替换为实际的上游仓库地址
git remote add upstream https://github.com/lifefloating/nestjs-project-template/
```

然后，您可以通过以下命令来验证是否添加成功：

```bash
git remote -v
```

您应该能看到类似以下的输出，其中包含了 `origin` 和 `upstream` 两个远程仓库：

```
origin    **************:your-username/your-repo.git (fetch)
origin    **************:your-username/your-repo.git (push)
upstream  https://github.com/lifefloating/nestjs-project-template/ (fetch)
upstream  https://github.com/lifefloating/nestjs-project-template/ (push)
```

### 2. 从上游仓库拉取更新

当您想要获取上游仓库的最新代码时，请执行以下命令：

```bash
git pull upstream main
```

这条命令会从 `upstream` 仓库的 `main` 分支拉取最新的代码，并尝试将其合并到您当前所在的本地分支。

### 3. 解决合并冲突

在拉取上游仓库更新的过程中，您可能会遇到“合并冲突”（Merge Conflicts）。这通常是因为您本地的代码和上游仓库的代码在同一个地方有不同的修改，Git 不知道该如何自动合并。

当遇到冲突时，您需要：

1.  **找到冲突文件**: Git 会在冲突的文件中用特殊的标记（如 `<<<<<<<`, `=======`, `>>>>>>>`）来标示出冲突的部分。
2.  **手动解决冲突**: 打开这些文件，根据您的需求，决定保留哪部分代码，或者将两部分代码进行整合。
3.  **暂存并提交**: 在解决了所有冲突之后，使用以下命令来完成合并：

    ```bash
    git add .
    git commit -m "Merge remote-tracking branch 'upstream/main'"
    ```

### 4. 推送更新到您自己的仓库

在成功合并了上游仓库的更新并解决了所有冲突之后，您可以将这些更新推送到您自己的 `origin` 仓库：

```bash
git push origin main
```

## 特殊情况：`refusing to merge unrelated histories`

如果您在 `pull` 的时候遇到 `refusing to merge unrelated histories` 的错误，这通常意味着您的项目和上游项目的 Git 历史没有共同的起点。

在这种情况下，您可以在 `pull` 命令中添加 `--allow-unrelated-histories` 标志来强制合并：

```bash
git pull upstream main --allow-unrelated-histories
```
