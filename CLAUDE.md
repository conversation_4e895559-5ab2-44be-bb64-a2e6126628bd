# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

A modern NestJS project template built with Fastify, Prisma ORM, and MongoDB. Features comprehensive tooling including multi-cloud storage (AWS S3, Alibaba OSS, Tencent COS), JWT authentication with BetterAuth integration, Stripe payments, internationalization, and Docker support.

## Key Architecture

- **Framework**: NestJS 11.x with Fastify adapter
- **Database**: MongoDB with Prisma ORM
- **Authentication**: BetterAuth with JWT + social providers
- **Storage**: Unified interface for S3, AliOSS, Tencent COS
- **API**: REST with OpenAPI/Swagger documentation
- **Testing**: Jest with comprehensive unit/integration tests

## Core Commands

### Development
```bash
# Install dependencies
pnpm install

# Development with hot reload
pnpm start:dev

# Production build
pnpm build
pnpm start:prod

# Generate Prisma client
pnpm prisma:generate

# Database seeding
pnpm db:seed
```

### Testing
```bash
# Unit tests
pnpm test

# Watch mode
pnpm test:watch

# Coverage
pnpm test:cov

# E2E tests
pnpm test:e2e

# Single test file
pnpm test -- src/users/services/users.service.spec.ts
```

### Code Quality
```bash
# Linting
pnpm lint
pnpm lint:fix

# Formatting
pnpm format
```

### Database
```bash
# Prisma Studio
pnpm prisma:studio

# Export OpenAPI docs
pnpm openapi:export
```

## Environment Setup

Copy `.env.example` to `.env` and configure:
- `DATABASE_URL`: MongoDB connection string
- `JWT_SECRET`: JWT signing secret
- Storage provider credentials (S3/AliOSS/Tencent COS)
- OAuth provider secrets
- Stripe keys
- Mail configuration

## Key Module Structure

```
src/
├── auth/           # BetterAuth integration
├── users/          # User management
├── storage/        # Multi-cloud storage
├── stripe/         # Payment processing
├── mailer/         # Email service
├── prisma/         # Database service
├── logger/         # Pino logger
├── config/         # Environment configuration
├── i18n/           # Internationalization
└── common/         # Shared utilities
```

## Storage Providers

The storage system supports three providers via unified interface:
- **AWS S3**: `s3-storage.provider.ts`
- **Alibaba OSS**: `alioss-storage.provider.ts` 
- **Tencent COS**: `tencentcos-storage.provider.ts`

Switch via `STORAGE_PROVIDER` env var: `s3`, `alioss`, or `tencentoss`.

## Authentication

Uses BetterAuth with:
- JWT tokens (access + refresh)
- Social login (GitHub, Apple, etc.)
- Session management
- Role-based access control

## API Documentation

Swagger UI available at `/apidoc` when running.
OpenAPI YAML export: `pnpm openapi:export`

## Testing Notes

- Jest configuration in `jest.config.ts`
- E2E tests in `test/` directory
- Module mocking patterns established
- Database cleanup utilities available