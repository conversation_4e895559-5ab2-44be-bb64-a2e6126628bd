# Installation and Running Guide

## Prerequisites

- [Node.js](https://nodejs.org/) v20 or higher
- [pnpm](https://pnpm.io/) v8 or higher
- [MongoDB](https://www.mongodb.com/) server

## Installation

1. Clone the repository
2. Install dependencies:

```bash
pnpm install
```

3. Set up environment variables:

```bash
cp .env.sample .env
# Edit .env with your configuration
```

4. Enable Global Configuration:

To ensure that the environment variables from your `.env` file are available throughout the application, you need to import and configure the `ConfigModule` in your root module (`src/app.module.ts`).

Open `src/app.module.ts` and add `ConfigModule.forRoot({ isGlobal: true })` to the `imports` array:

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
// ... other imports

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true, // Make the configuration global
    }),
    // ... other modules
  ],
  // ...
})
export class AppModule {}
```

This change ensures that all other modules in your application can access the environment variables without needing to import `ConfigModule` repeatedly.

5. Generate Prisma client:

```bash
pnpm prisma:generate
```

5. Seed the database (optional):

```bash
pnpm db:seed (需先看 DOCKER_MONGODB_GUIDE.md )
```

## Running the Application

Development mode:

```bash
pnpm start:dev
```

Production mode:

```bash
pnpm build
pnpm start:prod
```

## Code Quality

### Linting code:

```bash
# Check for ESLint errors
pnpm lint:check

# Fix ESLint errors automatically
pnpm lint
```

> **Note:** ESLint configuration is inspired by [brocoders/nestjs-boilerplate](https://github.com/brocoders/nestjs-boilerplate)

### Formatting code:

```bash
pnpm format
```

## Testing

Running unit tests:

```bash
pnpm test
```

Running e2e tests:

```bash
pnpm test:e2e
```

## API Documentation

API documentation is available at `/apidoc` when the application is running. It provides an interactive interface to explore and test the API endpoints.

http://localhost:7009/apidoc#/