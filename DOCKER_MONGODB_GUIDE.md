# Docker MongoDB 完整配置指南

> 适用于首次使用本项目的开发者，从零开始配置 MongoDB 环境

## 📋 准备工作

### 1. 确保已安装必要工具
```bash
# 检查 Docker 是否已安装
docker --version

# 检查 Docker Compose 是否已安装
docker-compose --version
```

### 2. 克隆项目并进入目录
```bash
cd /Users/<USER>/Desktop/2025-code/nest_wallpaper
```

## 🚀 快速开始（推荐方式）

### 方法1：使用 Docker Compose（最简单）
```bash
# 启动 MongoDB（已配置好 replica set）
docker-compose -f docker-compose.dev.yml up -d mongodb

# 等待 10 秒让 MongoDB 完全启动
sleep 10

# 检查是否正常运行
docker-compose -f docker-compose.dev.yml ps
```

### 方法2：手动配置（了解原理）

#### 步骤1：清理可能存在的冲突容器
```bash
# 停止并删除可能存在的旧容器
docker stop mongodb-dev 2>/dev/null || true
docker rm mongodb-dev 2>/dev/null || true
```

#### 步骤2：启动 MongoDB 容器
```bash
docker run -d \
  --name mongodb-dev \
  -p 27017:27017 \
  mongo:8.0 \
  --replSet rs0 \
  --bind_ip_all
```

#### 步骤3：初始化 Replica Set
```bash
# 等待 MongoDB 启动完成
sleep 5

# 初始化 replica set
docker exec mongodb-dev mongosh --eval "rs.initiate({_id: 'rs0', members: [{_id: 0, host: 'localhost:27017'}]})"
```

#### 步骤4：配置环境变量
编辑项目根目录下的 `.env` 文件，确保包含：
```bash
DATABASE_URL=mongodb://localhost:27017/nestjs_practice?replicaSet=rs0
```

## ✅ 验证安装

### 1. 检查 MongoDB 状态
```bash
# 查看容器运行状态
docker ps | grep mongo

# 检查 replica set 状态
docker exec mongodb-dev mongosh --eval "rs.status()" | grep -A 3 "stateStr"
```

### 2. 测试连接
```bash
# 使用 mongosh 连接测试
docker exec -it mongodb-dev mongosh --eval "db.runCommand({ping: 1})"
```

### 3. 启动应用测试
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm start:dev

# 测试 API（在新终端）
curl -X POST http://localhost:7009/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword",
    "firstName": "Test",
    "lastName": "User"
  }'
```

## 🔧 常见问题解决

### 问题1：端口被占用
```bash
# 查看哪个进程占用了 27017 端口
lsof -i :27017

# 如果是 Docker 容器占用
docker stop $(docker ps -q --filter publish=27017)
```

### 问题2：连接超时
```bash
# 重启容器
docker restart mongodb-dev

# 重新初始化 replica set
docker exec mongodb-dev mongosh --eval "rs.initiate({_id: 'rs0', members: [{_id: 0, host: 'localhost:27017'}]})"
```

### 问题3：权限问题
```bash
# 确保 Docker 有权限运行
sudo docker run hello-world
```

## 📊 日常操作命令

### 查看日志
```bash
# 实时查看 MongoDB 日志
docker logs -f mongodb-dev

# 查看最后 50 行日志
docker logs mongodb-dev --tail 50
```

### 数据备份与恢复
```bash
# 备份数据
docker exec mongodb-dev mongodump --out /tmp/backup
docker cp mongodb-dev:/tmp/backup ./mongodb-backup-$(date +%Y%m%d)

# 恢复数据
docker cp ./mongodb-backup-20250725 mongodb-dev:/tmp/backup
docker exec mongodb-dev mongorestore /tmp/backup
```

### 进入容器内部
```bash
# 进入 MongoDB 容器
docker exec -it mongodb-dev bash

# 在容器内使用 mongosh
mongosh
```

### 清理环境
```bash
# 停止并删除容器
docker stop mongodb-dev && docker rm mongodb-dev

# 清理无用镜像
docker system prune -f
```

## 🎯 成功标志

当你完成所有步骤后，应该看到：

1. **容器状态**：`docker ps` 显示 mongodb-dev 容器正在运行
2. **连接成功**：`mongosh` 可以正常连接
3. **应用启动**：NestJS 应用启动无报错
4. **API 响应**：用户创建接口返回 201 状态码

## 📞 寻求帮助

如果遇到问题：
1. 检查 Docker 是否正常运行：`docker ps`
2. 查看 MongoDB 日志：`docker logs mongodb-dev`
3. 确认端口是否开放：`netstat -an | grep 27017`
4. 检查环境变量是否正确配置