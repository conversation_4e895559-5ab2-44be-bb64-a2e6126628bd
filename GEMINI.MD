# Gemini Project Configuration

This file stores project-specific context, configurations, and important details for the Gemini agent to ensure its interactions are accurate and efficient.

## Project Overview

*   **Name:** nest_wallpaper
*   **Description:** A NestJS-based application for managing wallpapers.
*   **Primary Language:** TypeScript
*   **Framework:** NestJS

## Key Commands

*   **Installation:** `pnpm install`
*   **Running (Dev):** `pnpm run start:dev`
*   **Testing:** `pnpm run test`
*   **Linting:** `pnpm run lint`
*   **Build:** `pnpm run build`

## Important Notes

*   This project uses Prisma for database management. The schema is located at `prisma/schema.prisma`.
*   Configuration is managed through the `src/config` module. Environment-specific configurations are in `src/config/envs`.
*   The project follows conventional commit standards (see `COMMIT_CONVENTION.md`).
