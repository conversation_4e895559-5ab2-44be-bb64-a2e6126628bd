{"name": "nestjs-project-template", "version": "1.0.0", "description": "NestJS project structure template with best practices using Fastify, Prisma, MongoDB, and SWC", "main": "dist/main.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prepare": "husky", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "openapi:export": "curl -s http://localhost:7009/openapi.yaml > openapi/openapi.yaml && echo 'OpenAPI documentation exported from running server to openapi/ directory'"}, "keywords": [], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/lifefloating"}, "license": "MIT", "packageManager": "pnpm@10.13.1", "engines": {"node": ">=20.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.775.0", "@aws-sdk/lib-storage": "^3.775.0", "@aws-sdk/s3-request-presigner": "^3.775.0", "@fastify/compress": "^8.0.1", "@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/multipart": "^9.0.3", "@nestjs/common": "^11.0.12", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.12", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.0.12", "@nestjs/swagger": "^11.1.0", "@prisma/client": "^6.5.0", "@types/lodash": "^4.17.16", "ali-oss": "^6.22.0", "bcryptjs": "^3.0.2", "better-auth": "^1.2.4", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cos-nodejs-sdk-v5": "^2.14.7", "dayjs": "^1.11.13", "dotenv": "^17.0.0", "fastify": "^5.2.2", "fastify-multer": "^2.0.3", "joi": "^17.13.3", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "mjml": "^4.15.3", "mongodb": "^6.15.0", "nestjs-i18n": "^10.5.1", "nodemailer": "^7.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino": "^9.6.0", "pino-datadog-transport": "^3.0.6", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "stripe": "^17.7.0", "uuid": "^11.1.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.23.0", "@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.2", "@nestjs/testing": "^11.0.12", "@swc/cli": "^0.7.0", "@swc/core": "^1.11.13", "@types/ali-oss": "^6.16.11", "@types/jest": "^30.0.0", "@types/js-yaml": "^4.0.9", "@types/node": "^22.13.14", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "eslint": "^9.23.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-prettier": "5.5.3", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^30.0.0", "prettier": "^3.5.3", "prisma": "^6.5.0", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2", "typescript-eslint": "^8.28.0"}}