{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": false, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "paths": {"@app/*": ["src/*"], "@config/*": ["src/config/*"], "@common/*": ["src/common/*"], "@shared/*": ["src/shared/*"], "@core/*": ["src/core/*"]}}, "exclude": ["node_modules", "dist", "test", "**/*.spec.ts"]}